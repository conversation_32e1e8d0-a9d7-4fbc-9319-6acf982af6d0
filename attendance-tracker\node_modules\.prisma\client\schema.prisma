// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// User model for employees and admins
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  password  String
  role      UserRole @default(EMPLOYEE)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  faceProfile       FaceProfile?
  attendanceRecords AttendanceRecord[]

  @@map("users")
}

// Face profile for facial recognition
model FaceProfile {
  id        String   @id @default(cuid())
  userId    String   @unique
  faceData  String // JSON string containing face descriptors
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("face_profiles")
}

// Location boundaries for geofencing
model Location {
  id        String   @id @default(cuid())
  name      String
  address   String
  latitude  Float
  longitude Float
  radius    Float // in meters
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  attendanceRecords AttendanceRecord[]

  @@map("locations")
}

// Attendance records
model AttendanceRecord {
  id         String         @id @default(cuid())
  userId     String
  locationId String
  type       AttendanceType
  timestamp  DateTime       @default(now())
  latitude   Float?
  longitude  Float?
  notes      String?
  isVerified Boolean        @default(false)
  createdAt  DateTime       @default(now())

  // Relations
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  location Location @relation(fields: [locationId], references: [id])

  @@map("attendance_records")
}

// Enums
enum UserRole {
  ADMIN
  HR
  EMPLOYEE
}

enum AttendanceType {
  CHECK_IN
  CHECK_OUT
}
