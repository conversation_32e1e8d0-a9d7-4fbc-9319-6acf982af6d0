import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('Seeding database...')

  // Create admin user
  const adminPassword = await bcrypt.hash('admin123', 12)
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'System Administrator',
      password: adminPassword,
      role: 'ADMIN'
    }
  })

  // Create HR user
  const hrPassword = await bcrypt.hash('hr123', 12)
  const hr = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'HR Manager',
      password: hrPassword,
      role: 'HR'
    }
  })

  // Create employee user
  const employeePassword = await bcrypt.hash('employee123', 12)
  const employee = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '<PERSON>',
      password: employeePassword,
      role: 'EMPLOYEE'
    }
  })

  // Create office locations
  const mainOffice = await prisma.location.upsert({
    where: { id: 'main-office' },
    update: {},
    create: {
      id: 'main-office',
      name: 'Main Office',
      address: '123 Business Street, City, State 12345',
      latitude: 40.7128,
      longitude: -74.0060,
      radius: 100 // 100 meters
    }
  })

  const branchOffice = await prisma.location.upsert({
    where: { id: 'branch-office' },
    update: {},
    create: {
      id: 'branch-office',
      name: 'Branch Office',
      address: '456 Corporate Ave, City, State 12345',
      latitude: 40.7589,
      longitude: -73.9851,
      radius: 150 // 150 meters
    }
  })

  console.log('Database seeded successfully!')
  console.log('Created users:')
  console.log('- Admin: <EMAIL> / admin123')
  console.log('- HR: <EMAIL> / hr123')
  console.log('- Employee: <EMAIL> / employee123')
  console.log('Created locations:')
  console.log('- Main Office')
  console.log('- Branch Office')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
