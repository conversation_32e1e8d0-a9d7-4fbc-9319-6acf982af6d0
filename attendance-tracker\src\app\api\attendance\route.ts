import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { validateGeofence } from '@/lib/geolocation'
import { verifyFaceMatch, stringToDescriptor } from '@/lib/face-recognition'
import { AttendanceType } from '@prisma/client'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId') || session.user.id
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    // Check if user can access other user's data
    if (userId !== session.user.id && session.user.role === 'EMPLOYEE') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const whereClause: any = { userId }

    if (startDate && endDate) {
      whereClause.timestamp = {
        gte: new Date(startDate),
        lte: new Date(endDate)
      }
    }

    const attendanceRecords = await prisma.attendanceRecord.findMany({
      where: whereClause,
      include: {
        user: {
          select: { id: true, name: true, email: true }
        },
        location: true
      },
      orderBy: { timestamp: 'desc' }
    })

    return NextResponse.json(attendanceRecords)
  } catch (error) {
    console.error('Error fetching attendance records:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { 
      type, 
      latitude, 
      longitude, 
      faceDescriptor, 
      notes 
    } = await request.json()

    // Validate input
    if (!type || !latitude || !longitude || !faceDescriptor) {
      return NextResponse.json(
        { error: 'Type, location, and face data are required' },
        { status: 400 }
      )
    }

    // Get user's face profile
    const userFaceProfile = await prisma.faceProfile.findUnique({
      where: { userId: session.user.id }
    })

    if (!userFaceProfile || !userFaceProfile.isActive) {
      return NextResponse.json(
        { error: 'Face profile not found. Please register your face first.' },
        { status: 400 }
      )
    }

    // Verify face match
    const storedDescriptor = stringToDescriptor(userFaceProfile.faceData)
    const currentDescriptor = new Float32Array(faceDescriptor)
    
    if (!verifyFaceMatch(storedDescriptor, currentDescriptor)) {
      return NextResponse.json(
        { error: 'Face verification failed' },
        { status: 400 }
      )
    }

    // Get allowed locations
    const allowedLocations = await prisma.location.findMany({
      where: { isActive: true }
    })

    // Validate geofence
    const geofenceResult = validateGeofence(
      { latitude, longitude },
      allowedLocations
    )

    if (!geofenceResult.isValid) {
      return NextResponse.json(
        { 
          error: 'You are not within an allowed location',
          distance: geofenceResult.distance,
          nearestLocation: geofenceResult.location.name
        },
        { status: 400 }
      )
    }

    // Create attendance record
    const attendanceRecord = await prisma.attendanceRecord.create({
      data: {
        userId: session.user.id,
        locationId: geofenceResult.location.id,
        type: type as AttendanceType,
        latitude,
        longitude,
        notes,
        isVerified: true
      },
      include: {
        user: {
          select: { id: true, name: true, email: true }
        },
        location: true
      }
    })

    return NextResponse.json(
      { 
        message: 'Attendance recorded successfully', 
        record: attendanceRecord 
      },
      { status: 201 }
    )
  } catch (error) {
    console.error('Error recording attendance:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
