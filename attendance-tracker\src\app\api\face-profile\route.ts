import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const faceProfile = await prisma.faceProfile.findUnique({
      where: { userId: session.user.id }
    })

    return NextResponse.json({ 
      hasProfile: !!faceProfile,
      profile: faceProfile ? { id: faceProfile.id, isActive: faceProfile.isActive } : null
    })
  } catch (error) {
    console.error('Error fetching face profile:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { faceData } = await request.json()

    if (!faceData) {
      return NextResponse.json(
        { error: 'Face data is required' },
        { status: 400 }
      )
    }

    // Check if user already has a face profile
    const existingProfile = await prisma.faceProfile.findUnique({
      where: { userId: session.user.id }
    })

    let faceProfile

    if (existingProfile) {
      // Update existing profile
      faceProfile = await prisma.faceProfile.update({
        where: { userId: session.user.id },
        data: { faceData, isActive: true }
      })
    } else {
      // Create new profile
      faceProfile = await prisma.faceProfile.create({
        data: {
          userId: session.user.id,
          faceData
        }
      })
    }

    return NextResponse.json(
      { message: 'Face profile saved successfully', profile: faceProfile },
      { status: 201 }
    )
  } catch (error) {
    console.error('Error saving face profile:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
