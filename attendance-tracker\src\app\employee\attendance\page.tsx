'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState, useRef } from 'react'
import { loadFaceApiModels, detectFace, descriptorToString } from '@/lib/face-recognition'
import { getCurrentLocation } from '@/lib/geolocation'
import { AttendanceType } from '@/types'

export default function AttendancePage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const videoRef = useRef<HTMLVideoElement>(null)
  
  const [isLoading, setIsLoading] = useState(true)
  const [isRecording, setIsRecording] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [stream, setStream] = useState<MediaStream | null>(null)
  const [modelsLoaded, setModelsLoaded] = useState(false)
  const [faceDetected, setFaceDetected] = useState(false)
  const [attendanceType, setAttendanceType] = useState<AttendanceType>('CHECK_IN')
  const [location, setLocation] = useState<{ latitude: number; longitude: number } | null>(null)
  const [notes, setNotes] = useState('')

  useEffect(() => {
    if (status === 'loading') return

    if (!session) {
      router.push('/auth/signin')
      return
    }

    if (session.user.role === 'ADMIN' || session.user.role === 'HR') {
      router.push('/admin/dashboard')
      return
    }

    initializeCamera()
    loadModels()
    getLocation()
  }, [session, status, router])

  const loadModels = async () => {
    try {
      await loadFaceApiModels()
      setModelsLoaded(true)
    } catch (error) {
      console.error('Error loading models:', error)
      setError('Failed to load face recognition models')
    }
  }

  const initializeCamera = async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: { 
          width: 640, 
          height: 480,
          facingMode: 'user'
        }
      })
      
      setStream(mediaStream)
      
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream
        videoRef.current.onloadedmetadata = () => {
          setIsLoading(false)
        }
      }
    } catch (error) {
      console.error('Error accessing camera:', error)
      setError('Unable to access camera. Please ensure camera permissions are granted.')
      setIsLoading(false)
    }
  }

  const getLocation = async () => {
    try {
      const position = await getCurrentLocation()
      setLocation(position)
    } catch (error) {
      console.error('Error getting location:', error)
      setError('Unable to get your location. Please ensure location permissions are granted.')
    }
  }

  const detectFaceInVideo = async () => {
    if (!videoRef.current || !modelsLoaded) return

    try {
      const detection = await detectFace(videoRef.current)
      setFaceDetected(!!detection)
    } catch (error) {
      console.error('Error detecting face:', error)
    }
  }

  // Continuously detect faces
  useEffect(() => {
    if (!isLoading && modelsLoaded) {
      const interval = setInterval(detectFaceInVideo, 100)
      return () => clearInterval(interval)
    }
  }, [isLoading, modelsLoaded])

  const recordAttendance = async () => {
    if (!videoRef.current || !modelsLoaded || !location) {
      setError('Camera, models, or location not ready')
      return
    }

    setIsRecording(true)
    setError('')
    setSuccess('')

    try {
      const detection = await detectFace(videoRef.current)
      
      if (!detection) {
        setError('No face detected. Please ensure your face is clearly visible.')
        setIsRecording(false)
        return
      }

      // Record attendance
      const response = await fetch('/api/attendance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type: attendanceType,
          latitude: location.latitude,
          longitude: location.longitude,
          faceDescriptor: Array.from(detection.descriptor),
          notes: notes.trim() || undefined
        })
      })

      const data = await response.json()

      if (response.ok) {
        setSuccess(`${attendanceType === 'CHECK_IN' ? 'Check-in' : 'Check-out'} recorded successfully!`)
        setTimeout(() => {
          router.push('/employee/dashboard')
        }, 2000)
      } else {
        setError(data.error || 'Failed to record attendance')
      }
    } catch (error) {
      console.error('Error recording attendance:', error)
      setError('An error occurred while recording attendance')
    } finally {
      setIsRecording(false)
    }
  }

  const cleanup = () => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop())
    }
  }

  useEffect(() => {
    return cleanup
  }, [stream])

  if (status === 'loading' || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Record Attendance</h1>
              <p className="text-gray-600">Check in or check out with facial verification</p>
            </div>
            <button
              onClick={() => router.push('/employee/dashboard')}
              className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              Back to Dashboard
            </button>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {error && (
            <div className="mb-6 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
              {error}
            </div>
          )}
          
          {success && (
            <div className="mb-6 bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md">
              {success}
            </div>
          )}

          <div className="bg-white shadow rounded-lg p-6">
            {/* Attendance Type Selection */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Attendance Type
              </label>
              <div className="flex space-x-4">
                <button
                  onClick={() => setAttendanceType('CHECK_IN')}
                  className={`px-4 py-2 rounded-md font-medium ${
                    attendanceType === 'CHECK_IN'
                      ? 'bg-green-600 text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  Check In
                </button>
                <button
                  onClick={() => setAttendanceType('CHECK_OUT')}
                  className={`px-4 py-2 rounded-md font-medium ${
                    attendanceType === 'CHECK_OUT'
                      ? 'bg-red-600 text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  Check Out
                </button>
              </div>
            </div>

            {/* Notes */}
            <div className="mb-6">
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
                Notes (Optional)
              </label>
              <textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Add any notes about your attendance..."
              />
            </div>

            {/* Camera View */}
            <div className="text-center mb-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4">Face Verification</h2>
              
              <div className="flex justify-center mb-4">
                <div className="relative">
                  <video
                    ref={videoRef}
                    autoPlay
                    playsInline
                    muted
                    className="w-96 h-72 bg-black rounded-lg"
                  />
                  
                  {/* Face detection indicator */}
                  <div className={`absolute top-2 right-2 px-2 py-1 rounded text-xs font-medium ${
                    faceDetected 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {faceDetected ? 'Face Detected' : 'No Face Detected'}
                  </div>

                  {/* Location indicator */}
                  <div className={`absolute top-2 left-2 px-2 py-1 rounded text-xs font-medium ${
                    location 
                      ? 'bg-blue-100 text-blue-800' 
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {location ? 'Location Ready' : 'Getting Location...'}
                  </div>
                </div>
              </div>

              <button
                onClick={recordAttendance}
                disabled={isRecording || !faceDetected || !modelsLoaded || !location}
                className={`px-8 py-3 rounded-md text-lg font-medium text-white ${
                  attendanceType === 'CHECK_IN'
                    ? 'bg-green-600 hover:bg-green-700 disabled:bg-gray-400'
                    : 'bg-red-600 hover:bg-red-700 disabled:bg-gray-400'
                } disabled:cursor-not-allowed`}
              >
                {isRecording ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Recording...
                  </div>
                ) : (
                  `Record ${attendanceType === 'CHECK_IN' ? 'Check In' : 'Check Out'}`
                )}
              </button>
              
              <p className="mt-4 text-sm text-gray-500">
                {!modelsLoaded && 'Loading face recognition models...'}
                {!location && 'Getting your location...'}
                {modelsLoaded && location && !faceDetected && 'Position your face in the camera view'}
                {modelsLoaded && location && faceDetected && 'Ready to record attendance'}
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
