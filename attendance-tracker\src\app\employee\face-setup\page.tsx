'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState, useRef } from 'react'
import { loadFaceApiModels, detectFace, descriptorToString } from '@/lib/face-recognition'

export default function FaceSetup() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  
  const [isLoading, setIsLoading] = useState(true)
  const [isCapturing, setIsCapturing] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [stream, setStream] = useState<MediaStream | null>(null)
  const [modelsLoaded, setModelsLoaded] = useState(false)
  const [faceDetected, setFaceDetected] = useState(false)

  useEffect(() => {
    if (status === 'loading') return

    if (!session) {
      router.push('/auth/signin')
      return
    }

    if (session.user.role === 'ADMIN' || session.user.role === 'HR') {
      router.push('/admin/dashboard')
      return
    }

    initializeCamera()
    loadModels()
  }, [session, status, router])

  const loadModels = async () => {
    try {
      await loadFaceApiModels()
      setModelsLoaded(true)
      console.log('Face API models loaded')
    } catch (error) {
      console.error('Error loading models:', error)
      setError('Failed to load face recognition models')
    }
  }

  const initializeCamera = async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: { 
          width: 640, 
          height: 480,
          facingMode: 'user'
        }
      })
      
      setStream(mediaStream)
      
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream
        videoRef.current.onloadedmetadata = () => {
          setIsLoading(false)
        }
      }
    } catch (error) {
      console.error('Error accessing camera:', error)
      setError('Unable to access camera. Please ensure camera permissions are granted.')
      setIsLoading(false)
    }
  }

  const detectFaceInVideo = async () => {
    if (!videoRef.current || !modelsLoaded) return

    try {
      const detection = await detectFace(videoRef.current)
      setFaceDetected(!!detection)
      
      if (detection && canvasRef.current) {
        const canvas = canvasRef.current
        const ctx = canvas.getContext('2d')!
        
        canvas.width = videoRef.current.videoWidth
        canvas.height = videoRef.current.videoHeight
        
        ctx.clearRect(0, 0, canvas.width, canvas.height)
        
        // Draw detection box
        const { x, y, width, height } = detection.detection.box
        ctx.strokeStyle = '#00ff00'
        ctx.lineWidth = 2
        ctx.strokeRect(x, y, width, height)
      }
    } catch (error) {
      console.error('Error detecting face:', error)
    }
  }

  // Continuously detect faces
  useEffect(() => {
    if (!isLoading && modelsLoaded) {
      const interval = setInterval(detectFaceInVideo, 100)
      return () => clearInterval(interval)
    }
  }, [isLoading, modelsLoaded])

  const captureFace = async () => {
    if (!videoRef.current || !modelsLoaded) {
      setError('Camera or models not ready')
      return
    }

    setIsCapturing(true)
    setError('')
    setSuccess('')

    try {
      const detection = await detectFace(videoRef.current)
      
      if (!detection) {
        setError('No face detected. Please ensure your face is clearly visible.')
        setIsCapturing(false)
        return
      }

      // Convert descriptor to string for storage
      const faceData = descriptorToString(detection.descriptor)

      // Save to backend
      const response = await fetch('/api/face-profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ faceData })
      })

      const data = await response.json()

      if (response.ok) {
        setSuccess('Face profile saved successfully!')
        setTimeout(() => {
          router.push('/employee/dashboard')
        }, 2000)
      } else {
        setError(data.error || 'Failed to save face profile')
      }
    } catch (error) {
      console.error('Error capturing face:', error)
      setError('An error occurred while capturing your face')
    } finally {
      setIsCapturing(false)
    }
  }

  const cleanup = () => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop())
    }
  }

  useEffect(() => {
    return cleanup
  }, [stream])

  if (status === 'loading' || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Face Setup</h1>
              <p className="text-gray-600">Register your face for attendance verification</p>
            </div>
            <button
              onClick={() => router.push('/employee/dashboard')}
              className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              Back to Dashboard
            </button>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {error && (
            <div className="mb-6 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
              {error}
            </div>
          )}
          
          {success && (
            <div className="mb-6 bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md">
              {success}
            </div>
          )}

          <div className="bg-white shadow rounded-lg p-6">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Position Your Face</h2>
              <p className="text-gray-600">
                Look directly at the camera and ensure your face is well-lit and clearly visible.
              </p>
            </div>

            <div className="flex justify-center mb-6">
              <div className="relative">
                <video
                  ref={videoRef}
                  autoPlay
                  playsInline
                  muted
                  className="w-96 h-72 bg-black rounded-lg"
                />
                <canvas
                  ref={canvasRef}
                  className="absolute top-0 left-0 w-96 h-72 pointer-events-none"
                />
                
                {/* Face detection indicator */}
                <div className={`absolute top-2 right-2 px-2 py-1 rounded text-xs font-medium ${
                  faceDetected 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {faceDetected ? 'Face Detected' : 'No Face Detected'}
                </div>
              </div>
            </div>

            <div className="text-center">
              <button
                onClick={captureFace}
                disabled={isCapturing || !faceDetected || !modelsLoaded}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-6 py-3 rounded-md text-lg font-medium"
              >
                {isCapturing ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Capturing...
                  </div>
                ) : (
                  'Capture Face'
                )}
              </button>
              
              <p className="mt-4 text-sm text-gray-500">
                {!modelsLoaded && 'Loading face recognition models...'}
                {modelsLoaded && !faceDetected && 'Position your face in the camera view'}
                {modelsLoaded && faceDetected && 'Ready to capture your face'}
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
