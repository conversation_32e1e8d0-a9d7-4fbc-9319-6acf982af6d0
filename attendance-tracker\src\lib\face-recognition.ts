import * as faceapi from 'face-api.js'
import { FaceDetectionResult } from '@/types'

let isModelLoaded = false

/**
 * Load face-api.js models
 */
export async function loadFaceApiModels(): Promise<void> {
  if (isModelLoaded) return

  try {
    await Promise.all([
      faceapi.nets.tinyFaceDetector.loadFromUri('/models'),
      faceapi.nets.faceLandmark68Net.loadFromUri('/models'),
      faceapi.nets.faceRecognitionNet.loadFromUri('/models'),
      faceapi.nets.faceExpressionNet.loadFromUri('/models')
    ])
    isModelLoaded = true
    console.log('Face API models loaded successfully')
  } catch (error) {
    console.error('Error loading face API models:', error)
    throw error
  }
}

/**
 * Detect face and extract features from image
 * @param imageElement HTML image or video element
 * @returns Face detection result with descriptor
 */
export async function detectFace(
  imageElement: HTMLImageElement | HTMLVideoElement
): Promise<FaceDetectionResult | null> {
  try {
    const detection = await faceapi
      .detectSingleFace(imageElement, new faceapi.TinyFaceDetectorOptions())
      .withFaceLandmarks()
      .withFaceDescriptor()

    if (!detection) {
      return null
    }

    return {
      detection: detection.detection,
      descriptor: detection.descriptor
    }
  } catch (error) {
    console.error('Error detecting face:', error)
    return null
  }
}

/**
 * Compare two face descriptors
 * @param descriptor1 First face descriptor
 * @param descriptor2 Second face descriptor
 * @returns Similarity score (0-1, higher is more similar)
 */
export function compareFaces(
  descriptor1: Float32Array,
  descriptor2: Float32Array
): number {
  return 1 - faceapi.euclideanDistance(descriptor1, descriptor2)
}

/**
 * Verify if two faces match
 * @param descriptor1 First face descriptor
 * @param descriptor2 Second face descriptor
 * @param threshold Similarity threshold (default: 0.6)
 * @returns True if faces match
 */
export function verifyFaceMatch(
  descriptor1: Float32Array,
  descriptor2: Float32Array,
  threshold: number = 0.6
): boolean {
  const similarity = compareFaces(descriptor1, descriptor2)
  return similarity >= threshold
}

/**
 * Convert Float32Array to string for storage
 * @param descriptor Face descriptor
 * @returns JSON string
 */
export function descriptorToString(descriptor: Float32Array): string {
  return JSON.stringify(Array.from(descriptor))
}

/**
 * Convert string back to Float32Array
 * @param descriptorString JSON string
 * @returns Face descriptor
 */
export function stringToDescriptor(descriptorString: string): Float32Array {
  return new Float32Array(JSON.parse(descriptorString))
}

/**
 * Capture image from video stream
 * @param video Video element
 * @returns Canvas with captured image
 */
export function captureImageFromVideo(video: HTMLVideoElement): HTMLCanvasElement {
  const canvas = document.createElement('canvas')
  canvas.width = video.videoWidth
  canvas.height = video.videoHeight
  
  const ctx = canvas.getContext('2d')!
  ctx.drawImage(video, 0, 0, canvas.width, canvas.height)
  
  return canvas
}
