import { LocationData, GeofenceValidation, Location } from '@/types'

/**
 * Calculate distance between two coordinates using Haversine formula
 * @param lat1 Latitude of first point
 * @param lon1 Longitude of first point
 * @param lat2 Latitude of second point
 * @param lon2 Longitude of second point
 * @returns Distance in meters
 */
export function calculateDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const R = 6371e3 // Earth's radius in meters
  const φ1 = (lat1 * Math.PI) / 180
  const φ2 = (lat2 * Math.PI) / 180
  const Δφ = ((lat2 - lat1) * Math.PI) / 180
  const Δλ = ((lon2 - lon1) * Math.PI) / 180

  const a =
    Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

  return R * c
}

/**
 * Get current user location
 * @returns Promise with location data
 */
export function getCurrentLocation(): Promise<LocationData> {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error('Geolocation is not supported by this browser'))
      return
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        resolve({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy
        })
      },
      (error) => {
        reject(error)
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000
      }
    )
  })
}

/**
 * Validate if user is within allowed location boundaries
 * @param userLocation Current user location
 * @param allowedLocations Array of allowed locations
 * @returns Validation result
 */
export function validateGeofence(
  userLocation: LocationData,
  allowedLocations: Location[]
): GeofenceValidation {
  for (const location of allowedLocations) {
    const distance = calculateDistance(
      userLocation.latitude,
      userLocation.longitude,
      location.latitude,
      location.longitude
    )

    if (distance <= location.radius) {
      return {
        isValid: true,
        distance,
        location
      }
    }
  }

  // Find closest location for error reporting
  const closestLocation = allowedLocations.reduce((closest, location) => {
    const distance = calculateDistance(
      userLocation.latitude,
      userLocation.longitude,
      location.latitude,
      location.longitude
    )
    
    if (!closest || distance < closest.distance) {
      return { location, distance }
    }
    return closest
  }, null as { location: Location; distance: number } | null)

  return {
    isValid: false,
    distance: closestLocation?.distance || 0,
    location: closestLocation?.location || allowedLocations[0]
  }
}
