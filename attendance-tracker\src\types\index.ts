import { User, UserRole, AttendanceType, AttendanceRecord, Location, FaceProfile } from '@prisma/client'

export type { User, UserRole, AttendanceType, AttendanceRecord, Location, FaceProfile }

export interface UserWithFaceProfile extends User {
  faceProfile?: FaceProfile | null
}

export interface AttendanceRecordWithDetails extends AttendanceRecord {
  user: User
  location: Location
}

export interface LocationData {
  latitude: number
  longitude: number
  accuracy?: number
}

export interface FaceDetectionResult {
  detection: any
  descriptor: Float32Array
}

export interface AttendanceStats {
  totalDays: number
  presentDays: number
  absentDays: number
  lateArrivals: number
  earlyDepartures: number
}

export interface GeofenceValidation {
  isValid: boolean
  distance: number
  location: Location
}
